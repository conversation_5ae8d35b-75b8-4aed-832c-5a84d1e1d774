{"name": "automatic_backend", "version": "1.0.0", "description": "Backend server for automatic project", "main": "index.js", "scripts": {"start": "yarn build && node ./dist/index.js", "dev": "yarn build && yarn watch", "watch": "node --watch --require esbuild-register --inspect=0.0.0.0:9229 src/index.ts", "build": "esbuild src/*.ts src/**/*.ts --outdir=dist --format=cjs --platform=node --minify --bundle --alias:@/*=./src/*", "format": "prettier --write .", "lint": "eslint --fix", "generate": "drizzle-kit generate", "migrate": "drizzle-kit migrate", "seed": "node scripts/seed.js"}, "lint-staged": {"*.ts": "eslint --fix"}, "author": "peweo", "license": "ISC", "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^5.0.0", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.14.1", "@types/pg": "^8.11.10", "drizzle-kit": "^0.28.1", "esbuild": "0.24.0", "esbuild-register": "3.6.0", "eslint": "^9.14.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.3.3", "typescript": "^5.6.3"}, "dependencies": {"axios": "^1.8.4", "bcryptjs": "^2.4.3", "bullmq": "^5.49.2", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dayjs": "^1.11.13", "express": "^4.21.2", "jsonwebtoken": "^9.0.2", "nanoid": "^5.1.0", "openai": "^4.95.1", "pg": "^8.14.0", "postgres": "^3.4.5", "zod": "^3.24.1", "drizzle-orm": "^0.43.1"}}