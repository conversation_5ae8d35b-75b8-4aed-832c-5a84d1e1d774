'use client'

import { checkPostStatus, getPosts, getProjectById, markPostAsRead } from '@/actions/project'
import { useParams, useRouter } from 'next/navigation'
import { useEffect, useRef, useState } from 'react'
import { DotLottieReact } from '@lottiefiles/dotlottie-react'
import Markdown from 'react-markdown'
import { useUserStore } from '@/store/userStore'
import { checkSubscriptionStatus } from '@/utils/subscription-check'
import Link from 'next/link'
import { twMerge } from 'tailwind-merge'

// Define types for our data
interface ProjectData {
	name: string
	price: string
	description: string
	keywords: string[]
	subreddits: string[]
	estimatedViews: number
	viewsPerComment: number
	potentialCustomers: number
	conversionRate: string
	potentialRevenue: string
	avgSale: string
}

interface PostData {
	id: string
	title: string
	subreddit: string
	selftext: string
	subscribers: number
	chosenReason: string
	iconImg?: string | null
	redditId?: string
	author?: string
	permalink?: string
	url?: string
	created?: Date
	score?: number
	numComments?: number
	thumbnail?: string
	isOriginalContent?: boolean
	isVideo?: boolean
	upvoteRatio?: number
	sendReplies?: boolean
	rawData?: any
	status?: string
	chosen?: boolean
	chosenAt?: Date,
	relevanceScore?: number
	read?: boolean
}

const ProjectPage = () => {
	const { id } = useParams<{ id: string }>()
	const [isLoading, setIsLoading] = useState(true)
	const [projectLoading, setProjectLoading] = useState(true)
	const firstLoad = useRef(true)
	const [projectData, setProjectData] = useState<PostData[]>([])
	const [modal, setModal] = useState<PostData | null>(null)
	const isLoadingRef = useRef(true)
	const [data, setData] = useState<ProjectData>({
		name: '',
		price: '$0.00',
		description: '',
		keywords: [],
		subreddits: [],
		estimatedViews: 0,
		viewsPerComment: 50,
		potentialCustomers: 0,
		conversionRate: '1%',
		potentialRevenue: '$0.00',
		avgSale: '$0.00',
	})
	const router = useRouter()

	useEffect(() => {
		if (!id) {
			return
		}
		if (!firstLoad.current) return
		firstLoad.current = false

		const fetchProjectDetails = async () => {
			try {
				// Check subscription status first
				const { user } = useUserStore.getState() as { user: any }
				if (!user || !user?.uid) {
					router.push('/login')
					return
				}
				// const isSubscriptionActive = await checkSubscriptionStatus(user.uid, router)
				// if (!isSubscriptionActive) {
				// 	return // Router will handle the redirect
				// }

				const { success, project } = await getProjectById(id)

				if (success && project) {
					// Parse keywords from string to array
					const keywordsArray = project.keywords
						? project.keywords.split(',').map((k: string) => k.trim())
						: []

					// Format average sale value
					const avgSale = `$${project.averageSaleValue.toFixed(2)}`
					// Update project data in state
					setData((prev) => ({
						...prev,
						name: project.name,
						description: project.description,
						keywords: keywordsArray,
						avgSale: avgSale,
						price: avgSale, // Using average sale as the price display
					}))

					setProjectLoading(false)
				}
			} catch (error) {
				console.error('Error fetching project details:', error)
			}
		}

		const handlePostStatus = async () => {
			let con = true
			try {
				const data = await checkPostStatus(id)
				console.log({ data })
				setIsLoading(data?.data?.status === 'pending')
				isLoadingRef.current = data?.data?.status === 'pending'
				if (data?.data?.status === 'completed') {
					handlePost()
					con = false
				}
			} catch (error) { }
			if (con) {
				setTimeout(handlePostStatus, 5000)
			}
		}

		const handlePost = async () => {
			const { success, data: postsData } = await getPosts(id)

			if (success && postsData) {
				// Convert the API response to our PostData type
				const typedPostsData: PostData[] = postsData.map((post: any) => ({
					id: post.id || '',
					title: post.title || '',
					subreddit: post.subreddit || '',
					selftext: post.selftext || '',
					subscribers: Number(post.subscribers) || 0,
					chosenReason: post.chosenReason || '',
					iconImg: post.iconImg,
					redditId: post.redditId,
					author: post.author,
					permalink: post.permalink,
					url: post.url,
					created: post.created ? new Date(post.created) : undefined,
					score: post.score,
					numComments: post.numComments,
					thumbnail: post.thumbnail,
					isOriginalContent: post.isOriginalContent,
					isVideo: post.isVideo,
					upvoteRatio: post.upvoteRatio,
					sendReplies: post.sendReplies,
					status: post.status,
					chosen: post.chosen,
					chosenAt: post.chosenAt ? new Date(post.chosenAt) : undefined,
					relevanceScore: post.relevanceScore,
					read: post.read || false,
				}))

				const estimatedViews = Math.floor((typedPostsData.length || 0) * 50)
				const potentialCustomers = Math.floor(estimatedViews * 0.01)
				// Get average sale value from current data state
				const avgSaleValue = Number.parseFloat(data.avgSale.replace('$', ''))

				// Calculate potential revenue
				const potentialRevenue = `$${(potentialCustomers * avgSaleValue).toFixed(2)}`
				setData((prev) => ({
					...prev,
					potentialCustomers,
					estimatedViews,
					conversionRate: '1%',
					potentialRevenue,
					viewsPerComment: 50,
					subreddits: typedPostsData
						.map((post: PostData) => `r/${post.subreddit}`)
						.filter((v: string, i: number, a: string[]) => a.indexOf(v) === i),
				}))

				setProjectData(typedPostsData)
				console.log({ isLoadingRef: isLoadingRef.current })
				if (isLoadingRef.current) {
					setTimeout(handlePost, 20000)
				}
			}
		}

		// Fetch project details and posts data
		fetchProjectDetails()
		handlePostStatus()
		handlePost()
	}, [id])
	// This would come from your actual data source

	const [content, setContent] = useState<string[]>([])
	const [contentLoading, setContentLoading] = useState<boolean>(false)
	const [viewPost, setViewPost] = useState<boolean>(false)
	const [additionalContext, setAdditionalContext] = useState<string>('')
	const [maxWords, setMaxWords] = useState<number>(60)
	const [showCopyToast, setShowCopyToast] = useState<boolean>(false)
	const [markAsReadLoading, setMarkAsReadLoading] = useState<boolean>(false)

	const markAsRead = async (postId: string) => {
		setMarkAsReadLoading(true)
		try {
			const result = await markPostAsRead(postId)
			if (result.success) {
				// Update the local state to reflect the change
				setProjectData(prevData =>
					prevData.map(post =>
						post.id === postId ? { ...post, read: true } : post
					)
				)
				// Update modal state if it's the current post
				if (modal?.id === postId) {
					setModal(prev => prev ? { ...prev, read: true } : null)
				}
			} else {
				console.error('Failed to mark post as read:', result.error)
			}
		} catch (error) {
			console.error('Error marking post as read:', error)
		} finally {
			setMarkAsReadLoading(false)
		}
	}
	const handleGenerateComment = async () => {
		setContentLoading(true)
		setContent([])

		try {
			// Fetch from our API endpoint
			const response = await fetch('/api/post', {
				method: 'POST',
				body: JSON.stringify({
					projectId: id,
					postId: modal?.id || '',
					additionalContext,
					maxWords,
				}),
			})
			const reader = response.body?.getReader()

			if (!reader) {
				console.error('No reader available')
				return
			}

			const decoder = new TextDecoder()

			// Read the stream
			while (true) {
				const { done, value } = await reader.read()
				if (done) break

				const text = decoder.decode(value)
				const lines = text.split('\n').filter((line) => line.trim() !== '')
				// const lines = text.trim()

				setContent((prev) => [...prev, ...lines])
			}
		} catch (error) {
			console.error('Error reading stream:', error)
		} finally {
			setContentLoading(false)
			setViewPost(true)
		}
	}
	const renderBadge = (post: PostData) => {
		if (post.read) {
			return (
				<div className="badge badge-success bg-green-100 text-green-800 border-green-200">
					Commented
				</div>
			)
		}
		return (
			<div className="badge badge-warning bg-amber-100 text-amber-800 border-amber-200">
				Pending
			</div>
		)
	}
	return (
		<div className="container mx-auto px-4 py-8 relative">
			{/* Toast notification */}
			{showCopyToast && (
				<div className="toast toast-top toast-end z-50">
					<div className="alert alert-success">
						<span>Copied to clipboard!</span>
					</div>
				</div>
			)}
			{/* Project Header */}
			<div className="bg-base-100 rounded-lg shadow-sm border border-base-200 p-4 mb-6">
				<div className="flex items-start justify-between">
					<div>
						<div className="flex items-center gap-2">
							<h1 className="text-2xl font-bold">{data.name}</h1>
							<div className="badge badge-primary">{data.price}</div>
						</div>
						<p className="text-gray-600 mt-1">{data.description}</p>

						<div className="mt-3">
							<span className="text-sm text-gray-500 mr-2">Keywords:</span>
							{data.keywords.slice(0, 5).map((keyword, index) => (
								<span key={index} className="badge badge-outline mr-2 mb-2">
									{keyword}
								</span>
							))}
							{data.keywords.length > 5 && (
								<span className="badge badge-secondary">+{data.keywords.length - 5}</span>
							)}
						</div>

						<div className="mt-2">
							<span className="text-sm text-gray-500 mr-2">Subreddits:</span>
							{data.subreddits.slice(0, 5).map((subreddit, index) => (
								<span key={index} className="badge badge-accent mr-2">
									{subreddit}
								</span>
							))}
							{data.subreddits.length > 5 && (
								<span className="badge badge-secondary">+{data.subreddits.length - 5}</span>
							)}
						</div>
					</div>

					{/* <div className="flex gap-2">
						<button type="button" className="btn btn-sm btn-outline">Strategy</button>
						<button type="button" className="btn btn-sm btn-outline">Edit</button>
						<button type="button" className="btn btn-sm btn-outline">Refresh</button>
					</div> */}
				</div>
			</div>

			{/* Revenue Forecast */}
			<div className="bg-base-100 rounded-lg shadow-sm border border-base-200 mb-6">
				<div className="p-4 border-b border-base-200">
					<h2 className="text-xl font-bold flex items-center">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							className="h-5 w-5 text-primary mr-2"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<title>Revenue Forecast</title>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
							/>
						</svg>
						Revenue Forecast
					</h2>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4">
					<div className="card bg-base-100 border border-base-200">
						<div className="card-body p-4">
							<div className="flex items-center mb-1">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									className="h-5 w-5 text-blue-500 mr-2"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
									/>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
									/>
								</svg>
								<h3 className="card-title text-sm">Estimated Views</h3>
								<div className="tooltip ml-1" data-tip="Estimated views based on historical data">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-4 w-4 text-gray-400"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<title>Estimated Views</title>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
										/>
									</svg>
								</div>
							</div>
							<p className="text-3xl font-bold">{data.estimatedViews.toLocaleString()}</p>
							<p className="text-gray-500 text-sm">{data.viewsPerComment} views per comment on post</p>
						</div>
					</div>

					<div className="card bg-base-100 border border-base-200">
						<div className="card-body p-4">
							<div className="flex items-center mb-1">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									className="h-5 w-5 text-primary mr-2"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<title>Potential Customers</title>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
									/>
								</svg>
								<h3 className="card-title text-sm">Potential Customers</h3>
								<div className="tooltip ml-1" data-tip="Estimated customer conversion from views">
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-4 w-4 text-gray-400"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<title>Potential Customers</title>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
										/>
									</svg>
								</div>
							</div>
							<p className="text-3xl font-bold">{data.potentialCustomers}</p>
							<p className="text-gray-500 text-sm">{data.conversionRate} conversion rate</p>
						</div>
					</div>

					<div className="card bg-base-100 border border-base-200">
						<div className="card-body p-4">
							<div className="flex items-center mb-1">
								<svg
									xmlns="http://www.w3.org/2000/svg"
									className="h-5 w-5 text-green-500 mr-2"
									fill="none"
									viewBox="0 0 24 24"
									stroke="currentColor"
								>
									<title>Potential Revenue</title>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
									/>
								</svg>
								<h3 className="card-title text-sm">Potential Revenue</h3>
								<div
									className="tooltip ml-1"
									data-tip="Projected revenue based on current conversion rates"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="h-4 w-4 text-gray-400"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<title>Potential Revenue</title>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
										/>
									</svg>
								</div>
							</div>
							<p className="text-3xl font-bold text-green-600">${Number.parseFloat(data.avgSale.replace('$', '') || "0") * data.potentialCustomers}</p>
							<p className="text-gray-500 text-sm">Avg. sale: {data.avgSale}</p>
						</div>
					</div>
				</div>
			</div>

			{/* Leads Tabs */}
			<div className="bg-base-100 rounded-lg shadow-sm border border-base-200 mb-6">
				<div className="flex border-b border-base-200">
					<button className="tab tab-bordered tab-active px-6 py-3 font-medium" type="button">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							className="h-5 w-5 mr-2 inline"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<title>Active Leads</title>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M13 10V3L4 14h7v7l9-11h-7z"
							/>
						</svg>
						Active Leads
						<span className="badge badge-sm ml-2">{projectData.length}</span>
					</button>
					<button className="tab tab-bordered px-6 py-3 font-medium" type="button">
						<svg
							xmlns="http://www.w3.org/2000/svg"
							className="h-5 w-5 mr-2 inline"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<title>Responded</title>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
							/>
						</svg>
						Responded
					</button>
				</div>

				<div className="p-4">
					{/* Quality Score Filter */}
					<div className="flex justify-between items-center mb-4">
						<div>
							<p className="text-sm text-gray-500">
								{projectData.length > 0
									? `Showing 1 - ${Math.min(projectData.length, 10)} of ${projectData.length} leads`
									: 'No leads found'}
							</p>
						</div>
						<div className="flex items-center">
							<span className="text-sm text-gray-500 mr-2">Quality Score:</span>
							<div className="flex gap-2">
								<span className="badge badge-success gap-1">4-5: Excellent</span>
								<span className="badge badge-info gap-1">2-3: Good</span>
								<span className="badge badge-ghost gap-1">1: Basic</span>
							</div>
						</div>
					</div>
					{isLoading && (
						<div className="card bg-base-100 shadow-sm text-center flex justify-center items-center border border-base-200 rounded-xl p-4 mb-4 animate-pulse">
							<DotLottieReact
								src="https://lottie.host/135db459-6f7a-4e14-800d-ca855f3dd105/0T9DQFohbE.lottie"
								loop
								autoplay
								className="max-w-[500px] w-full"
							/>
							<p className="text-gray-500 -mt-8">Generating leads...</p>
						</div>
					)}
					{/* Leads Grid */}
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						{projectData.map((item: PostData) => {
							const blur = item.url === '#'
							return (
								<div
									key={item?.id}
									className="card bg-base-100 shadow-sm border border-base-200 rounded-xl relative"
								>
									<div className="p-2 flex items-center gap-2">
										<div className="avatar">
											<div className="bg-primary text-white rounded w-8 h-8 pt-1 flex items-center justify-center text-center">
												{item.subreddit.substring(0, 1)}
											</div>
										</div>
										<span className="text-sm font-medium">r/{item.subreddit}</span>
										<div className="ml-auto flex gap-1">
											<div className="badge badge-warning bg-green-500 text-white border-green-200">
												{item.relevanceScore || '0'}
											</div>
											{
												renderBadge(item)
											}

											{/* <div className="badge badge-success bg-green-100 text-green-800 border-green-200">
											t
										</div> */}
											<div className="tooltip tooltip-bottom">
												<div className="tooltip-content">
													<p className="text-xs p-2 rounded-lg bg-gray-100 text-left">
														{item.chosenReason}
													</p>
												</div>
												<div className="bg-amber-100 w-6 h-6 rounded-full flex items-center justify-center">
													<svg
														xmlns="http://www.w3.org/2000/svg"
														width="12"
														height="12"
														viewBox="0 0 64 64"
														fill="#f9be19"
														className="w-full h-full"
														aria-labelledby="starIconTitle"
													>
														<title id="starIconTitle">Star rating icon</title>
														<path d="m24 4 5.303 9.697L39 19l-9.697 5.303L24 34l-5.303-9.697L9 19l9.697-5.303zM45 29l3.536 6.464L55 39l-6.464 3.536L45 49l-3.536-6.464L35 39l6.464-3.536zM24 50l1.768 3.232L29 55l-3.232 1.768L24 60l-1.768-3.232L19 55l3.232-1.768z" />
													</svg>
												</div>
											</div>
										</div>
									</div>

									<div className="relative">
										<div className="px-4 py-3">
											<Link href={item.url || ''} target="_blank" className='before:absolute before:content-[" "] before:inset-0'>
												<h3 className={twMerge('font-bold text-base line-clamp-2', blur && 'blur-sm')}>{item.title}</h3>
											</Link>
											{/* <div className="text-sm text-gray-500 mt-1">
										{lead.author} • {lead.days} days ago
									</div> */}
										</div>

										<div className="px-4 pb-3 border-t border-gray-100 pt-3">
											<div className={twMerge('text-sm text-gray-700 line-clamp-5', blur && 'blur-sm')}>
												<Markdown>{item.selftext}</Markdown>
											</div>
										</div>
									</div>

									{/* <div className="px-4 pb-3">
									<div className="text-sm text-gray-600 mb-2">Matched Keywords:</div>
									<div className="flex flex-wrap gap-1"> */}
									{/* {lead.keywords.map((keyword, idx) => (
											<span key={idx} className="badge badge-sm bg-gray-100 text-gray-800 border-gray-200">{keyword}</span>
										))} */}
									{/* <span className="badge badge-sm bg-gray-100 text-gray-800 border-gray-200">customer engagement</span>
									</div>
								</div> */}

									<div className="mt-auto">
										{/* <div className="flex border-t border-gray-100">
										<button
											type="button"
											className="btn !rounded-none btn-ghost flex-1 justify-center py-3 text-sm font-normal"
										>
											<svg
												xmlns="http://www.w3.org/2000/svg"
												className="h-5 w-5 mr-1"
												fill="none"
												viewBox="0 0 24 24"
												stroke="currentColor"
											>
												<title>Copy</title>
												<path
													strokeLinecap="round"
													strokeLinejoin="round"
													strokeWidth={2}
													d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
												/>
											</svg>
											Copy
										</button>
									</div> */}

										<div className="grid grid-cols-2 gap-2 p-3">
											<button
												onClick={() => {
													setModal(item)
													setContentLoading(false)
													setContent([])
													setAdditionalContext('')
													setMaxWords(60)
													setViewPost(false)
												}}
												type="button"
												className="btn bg-purple-100 hover:bg-purple-200 border-none text-purple-800 font-medium"
											>
												<svg
													xmlns="http://www.w3.org/2000/svg"
													className="h-5 w-5 mr-1"
													fill="none"
													viewBox="0 0 24 24"
													stroke="currentColor"
													strokeWidth={1.5}
												>
													<title>Craft Reply</title>
													<path
														strokeLinecap="round"
														strokeLinejoin="round"
														d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z"
													/>
												</svg>
												Craft Reply
											</button>
											<button
												disabled
												type="button"
												className="btn bg-primary hover:bg-orange-600 border-none text-white font-medium"
											>
												<svg
													xmlns="http://www.w3.org/2000/svg"
													className="h-5 w-5 mr-1"
													fill="none"
													viewBox="0 0 24 24"
													stroke="currentColor"
													strokeWidth={1.5}
												>
													<title>Automatic</title>
													<path
														strokeLinecap="round"
														strokeLinejoin="round"
														d="M13 10V3L4 14h7v7l9-11h-7z"
													/>
												</svg>
												Automatic
											</button>
										</div>
									</div>
									{
										blur && (
											<div className="absolute inset-0 group flex justify-center items-center transition-all duration-300 bg-white/50">
												<Link href='/plans' className='btn btn-primary btn-sm !rounded duration-300 transition-all'>Subscribe now to access</Link>
											</div>
										)
									}
								</div>
							)
						})}
					</div>
				</div>
			</div>
			<dialog id="generate_comment_modal" className="modal" open={modal !== null}>
				<div className="modal-box">
					<form method="dialog">
						<button
							onClick={() => {
								setModal(null)
								setAdditionalContext('')
								setContent([])
								setShowCopyToast(false)
							}}
							className="btn btn-sm btn-circle btn-ghost absolute right-2 top-2"
							type="button"
						>
							✕
						</button>
					</form>
					<div className="flex items-center gap-2 mb-4">
						<h3 className="font-bold text-lg">Generate Reddit Comment</h3>
					</div>
					<p className="mb-4">
						Generate a persuasive comment for this post that subtly promotes your product.
					</p>

					<div className="bg-base-200 p-3 rounded-lg mb-4">
						<div className="flex items-center mb-2">
							<div className="avatar mr-2">
								<div className="w-6 h-6 rounded-md bg-red-500 flex items-center justify-center text-white text-xs font-bold"></div>
							</div>
							<span className="font-medium">r/{modal?.subreddit}</span>
						</div>
						<div className="bg-base-100 px-3 py-2 rounded-md">
							<h4 className="font-bold mb-1 line-clamp-1">{modal?.title}</h4>
							<div className="text-sm text-gray-500 mb-2">r/{modal?.subreddit}</div>
						</div>
					</div>

					<div className="mb-4">
						<div className="flex items-center gap-2 mb-2">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								className="h-5 w-5 text-primary"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								strokeWidth="2"
								strokeLinecap="round"
								strokeLinejoin="round"
							>
								<title>Additional Context</title>
								<rect x="3" y="4" width="18" height="18" rx="2" ry="2" />
								<line x1="16" y1="2" x2="16" y2="6" />
								<line x1="8" y1="2" x2="8" y2="6" />
								<line x1="3" y1="10" x2="21" y2="10" />
							</svg>
							<h4 className="font-medium">Additional Context (Optional)</h4>
						</div>
						<textarea
							className="textarea textarea-bordered w-full"
							placeholder="Add any specific points you want to highlight about your product..."
							value={additionalContext}
							onChange={(e) => setAdditionalContext(e.target.value)}
						/>
					</div>

					<div className="mb-6">
						<div className="flex items-center gap-2 mb-2">
							<svg
								xmlns="http://www.w3.org/2000/svg"
								className="h-5 w-5 text-primary"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								strokeWidth="2"
								strokeLinecap="round"
								strokeLinejoin="round"
							>
								<title>Maximum Words</title>
								<polyline points="4 7 4 4 20 4 20 7" />
								<line x1="9" y1="20" x2="15" y2="20" />
								<line x1="12" y1="4" x2="12" y2="20" />
							</svg>
							<h4 className="font-medium">Maximum Words</h4>
						</div>
						<div className="flex items-center gap-3">
							<input
								type="number"
								className="input input-bordered w-24"
								value={maxWords}
								min={10}
								max={250}
								onChange={(e) => {
									const value = Math.min(250, Math.max(10, parseInt(e.target.value) || 10))
									setMaxWords(value)
								}}
							/>
							<span className="text-sm text-gray-500">
								Shorter comments often perform better (10-250)
							</span>
						</div>
					</div>

					<div className='flex justify-center items-center gap-2'>
						<button
							className="btn btn-primary w-full flex-1"
							type="button"
							onClick={handleGenerateComment}
							disabled={contentLoading}
						>
							{contentLoading ? (
								<>
									<span className="loading loading-spinner" />
									Generating...
								</>
							) : (
								'Generate Comment'
							)}
						</button>
						{
							viewPost && (
								<Link
									className="btn btn-secondary w-full flex-1"
									type="button"
									href={modal?.url || ''}
									target="_blank"
								>
									View Post
								</Link>
							)}
					</div>
					<div className="mt-3">
						<button
							className="border border-gray-200 rounded-full p-2 w-full text-sm flex-1 hover:bg-gray-100 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
							type="button"
							disabled={markAsReadLoading || modal?.read}
							onClick={() => {
								if (!modal) return
								markAsRead(modal.id)
							}}
						>
							{markAsReadLoading ? (
								<span className="flex items-center justify-center">
									<svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
										<circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
										<path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
									</svg>
									Marking as read...
								</span>
							) : modal?.read ? (
								'Marked as commented'
							) : (
								'Mark as commented'
							)}
						</button>
					</div>
					<div className="mt-4">
						{content.length > 0 && (
							<div className="relative text-sm border-primary border rounded-md p-3 bg-base-100 text-left text-base-content group">
								<button
									onClick={() => {
										navigator.clipboard.writeText(content.join(''))
										setShowCopyToast(true)
										setTimeout(() => setShowCopyToast(false), 3000)
									}}
									className="absolute top-2 right-2 p-1 rounded-md bg-base-200 transition-colors opacity-0 group-hover:opacity-80 cursor-pointer"
									title="Copy to clipboard"
								>
									<svg
										xmlns="http://www.w3.org/2000/svg"
										className="size-4"
										fill="none"
										viewBox="0 0 24 24"
										stroke="currentColor"
									>
										<title>Copy to clipboard</title>
										<path
											strokeLinecap="round"
											strokeLinejoin="round"
											strokeWidth={2}
											d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"
										/>
									</svg>
								</button>
								{content.join('')}
								{contentLoading && (
									<span className="animate-ping mx-0.5 size-3 rounded-full bg-primary" />
								)}
							</div>
						)}
					</div>
				</div>
			</dialog>
		</div>
	)
}

export default ProjectPage
